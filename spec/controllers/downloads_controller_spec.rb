require 'rails_helper'
include ActiveSupport::Testing::TimeHelpers
require 'concerns/comments_controller_shared_examples'

RSpec.describe DownloadsController, type: :controller do

  include ActiveJob::TestHelper

  let(:user) {create(:user)}
  let(:download) {create(:download, title: 'Air免CD补丁', kind: 'nodvd', user: user, price: 10)}
  let(:subject) {create(:subject)}

  let(:valid_attributes) {
    {title: 'Air修正补丁', subject_id: create(:subject, name: 'Air').id, kind: 'patch', url: 'http://www.2dfan.com'}
  }

  let(:invalid_attributes) {
    {title: '', subject_id: subject.id, url: 'http://www.2dfan.com'}
  }

  let(:valid_session) { {} }

  describe "GET #index" do
    before do
      create_list(:download, 5)
    end

    subject { assigns(:downloads)}

    context 'right count' do
      it 'normal' do
        get :index

        expect(subject.size).to eq 5
      end

      it 'with deleted' do
        Download.last.destroy
        get :index

        expect(subject.size).to eq 4
      end

      describe 'with censor' do
        it 'no login' do
          Subject.last.update_attribute(:censor, 'need_login')
          get :index

          expect(subject.size).to eq 4
        end

        it 'with login' do
          login_user user
          user.update_attribute(:grade, 'newbie')
          Subject.update_all(censor: 2)
          Subject.first.update_attribute(:censor, 'need_login')
          get :index

          expect(subject.size).to eq 1
        end
      end
    end

    it 'paged' do
      Download::paginates_per 5
      create_list(:download, 2)
      get :index, params: {page: 2}

      expect(subject.size).to eq 2
    end

    it 'right order' do
      first = create(:download, created_at: Time.now.tomorrow)
      get :index

      expect(subject.first.id).to eq first.id
    end

    describe 'with params' do
      context 'with kind param' do
        it 'no page' do
          create_list(:download, 2, kind: 'nodvd')
          get :index, params: {kind: 'nodvd'}

          expect(subject.size).to eq 2
        end

        it 'paged' do
          Download::paginates_per 5
          create_list(:download, 2)
          get :index, params: {kind: 'cg_save', page: 2}

          expect(subject.size).to eq 2
        end
      end

      it 'with subject_id destroyed' do
        download = create(:download, user: user)
        download.subject.destroy
        ActsAsTaggableOn::Tagging.destroy_all
        get :index, params: {subject_id: download.subject_id}

        expect(response.status).to eq 404
      end

      it 'member_id' do
        login_user user
        create_list(:download, 2, user: user)
        get :index, params: {kind: 'cg_save', user_id: user.id}

        expect(subject.size).to eq 2
        expect(assigns(:count)).to eq 2
      end
    end
  end

  describe "POST #create" do
    describe 'no login' do
      it 'no record created' do
        post :create, params: {download: valid_attributes}

        expect(Download.all.size).to be_zero
      end

      it "auth login" do
        post :create, params: {download: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(:not_authenticated_users)
      end
    end

    describe "login" do
      before do
        login_user user
      end

      it "with valid params" do
        allow_any_instance_of(User).to receive(:risk_uploader?).and_return(false)
        post :create, params: {download: valid_attributes.merge(manual_price: 20, is_official: true)}

        expect(Download.all.size).to eq 1
        expect(assigns(:download).title).to eq 'Air修正补丁'
        expect(assigns(:download).price).to eq 20
        expect(assigns(:download).is_official).to be_truthy
      end

      it "local file scan job" do
        allow_any_instance_of(Download).to receive(:file).and_return(Ckeditor::AttachmentFile.new)
        allow_any_instance_of(Download).to receive(:upload_to_virustotal).and_call_original
        post :create, params: {download: valid_attributes}

        expect(enqueued_jobs.size).to eq 1
      end

     it 'verified author via json' do
        allow_any_instance_of(User).to receive(:is_verified?).and_return(true)
        post :create, params: {download: valid_attributes.merge(is_official: true, manual_price: 20, url: ''), pre_upload: true}

        expect(assigns(:download).is_official).to be_truthy
        expect(assigns(:download).price).to eq 20
        expect(assigns(:download).permanent_link).to eq '/json_upload/unfinished'
      end

      context 'merit' do
        context 'valid' do
          it 'normal user' do
            allow_any_instance_of(Download).to receive(:should_grant_reward?).and_return(true)
            post :create, params: {download: valid_attributes}

            Merit::Action.check_unprocessed
            expect(user.points).to eq 5
          end

          it 'newbie' do
            user.update_column(:reputation, -1)
            post :create, params: {download: valid_attributes}

            Merit::Action.check_unprocessed
            expect(user.points).to be_zero
          end
        end

        it 'invalid' do
          user.update(reputation: -1)
          post :create, params: {download: valid_attributes}

          Merit::Action.check_unprocessed
          expect(user.points).to be_zero
        end
      end

      context "with invalid params" do
        it 'blank title' do
          post :create, params: {download: invalid_attributes, format: :json}

          expect(Download.all.size).to be_zero
          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to eq ["游戏名称不能为空字符"]
        end

        it 'unverified user with is_official param' do
          allow_any_instance_of(User).to receive(:risk_uploader?).and_return(true)
          post :create, params: {download: valid_attributes.merge(is_official: true)}

          expect(assigns(:download).is_official).to be_falsey
        end

        it 'blank url' do
          allow_any_instance_of(Download).to receive(:check_source_url).and_call_original
          post :create, params: {download: valid_attributes.merge!({url: ''}), format: :json}

          expect(Download.all.size).to be_zero

          expect(response.status).to eq 422
          result = JSON.parse(response.body)
          expect(result['message']).to eq ['必须添加本地或者长效链']
        end
      end

      it "redirect" do
        post :create, params: {download: valid_attributes}

        expect(response.status).to eq 302
        expect(response).to redirect_to(Download.last)
      end
    end
  end

  describe "PUT #update" do
    it 'not authenticated' do
      put :update, params: {id: download.id, download: {title: 'Rance6全CG存档'}}

      expect(response.status).to eq 302
      expect(response).to redirect_to(:not_authenticated_users)
    end

    context "normal user" do
      before do
        login_user user
      end

      context "valid params" do
        it 'title and invalid manual price' do
          put :update, params: {id: download.id, download: {title: 'Rance6全CG存档', manual_price: 15}}

          expect(assigns(:download).title).to eq 'Rance6全CG存档'
          expect(assigns(:download).price).to eq 10
        end
=begin
        it 'file renew' do
          file =  fixture_file_upload("files/avatar.jpg")
          Ckeditor::AttachmentFile.create(attachable_id: download.id, attachable_type: 'Download', assetable: download.user)
          put :update, id: download.id, download: {title: 'Rance6全CG存档', file: file}

          expect(Ckeditor::AttachmentFile.all.size).to eq 1
          expect(assigns(:download).attachment.url).not_to be_empty
        end
=end
      end

      it "forbidden attribute" do
        person = create(:user)
        put :update, params: {id: download.id, download: {user_id: person.id}}

        expect(assigns(:download).user_id).not_to eq person.id
      end

      it 'invalid attributes' do
        create(:user)
        put :update, params: {id: download.id, download: invalid_attributes}

        expect(flash[:error]).to eq ['游戏名称不能为空字符']
      end

      # 考虑到网盘链失效需要长期补档的问题，不应该限制编辑权限
      it 'expired', skip: true do
        travel 60.days do
          put :update, params: {id: download.id, download: {title: 'Rance6全CG存档'}}

          expect(response.status).to eq 403
        end
      end
    end

    it "admin"

  end

  describe "#show" do
    it 'right attribute' do
      get :show, params: {id: download.id}

      expect(assigns(:download).title).to eq 'Air免CD补丁'
      expect(assigns(:download).kind_i18n).to eq '免DVD补丁'
      expect(assigns(:has_human_trans)).to be_falsey
    end

    it 'has human_trans' do
      create(:download, subject: subject, kind: 'human_trans')
      download = create(:download, subject: subject, kind: 'machine_trans')
      get :show, params: {id: download.id}

      expect(assigns(:has_human_trans)).to be_truthy
    end

    it 'censor' do
      download.subject.update_attribute(:censor, 'need_login')
      get :show, params: {:id => download.to_param}

      expect(response.status).to eq 404
    end

    it 'related resource' do
      create(:topic, subject_id: download.subject_id, type: 'Review')
      create_list(:download, 2, subject_id: download.subject_id, kind: 'cg_save')

      get :show, params: {id: download.id}

      expect(assigns(:related_downloads).size).to eq 2
      expect(assigns(:related_topics).size).to eq 1
    end

    it 'moved permanently' do
      parent = create(:download)
      download.update_column(:parent_id, parent.id)

      get :show, params: {id: download.id}

      expect(response.status).to eq 301
      expect(response).to redirect_to(parent)
    end
  end

  describe "GET #new" do
    before do
      login_user user
    end

    it 'valid' do
      get :new, params: {subject_id: subject.id}
      expect(assigns(:download)).to be_a_new(Download)
    end

    it 'no privilege' do
      subject.update(censor: 'no_newbie')
      user.update(grade: 'newbie')

      get :new, params: {subject_id: subject.id}
      expect(response.status).to eq 404
      expect(assigns(:download)).to be_nil
    end
  end

  describe 'GET #edit' do
    before do
      login_user user
    end

    it 'with right data structure' do
      download = create(:download, user: user, subject: subject, url: ['http://baidu.com, http://taobao.com'])

      get :edit, params: {id: download.id, subject_id: subject.id}
      expect(assigns(:subject)).to eq subject
      expect(assigns(:download).url).to eq 'http://baidu.com, http://taobao.com'
    end

    it 'render template' do
      get :edit, params: {id: download.id, subject_id: subject.id}
      expect(response).to render_template(:new)
    end
  end

  it_behaves_like 'comments controller shared examples' do
    let(:commentable) {create(:download)}
  end

  describe 'PUT #callback_string' do
    before do
      login_user user
    end

    it 'default' do
      put :callback_string, params: {}

      expect(assigns(:callback_string)).to eq 'eyJjYWxsYmFja1VybCI6Imh0dHA6Ly9hY2dob3N0LnZpcDo4ODAwL2Rvd25sb2Fkcy9vc3NfY2FsbGJhY2s/IiwiY2FsbGJhY2tCb2R5Ijoib2JqZWN0PSR7b2JqZWN0fVx1MDAyNnNpemU9JHtzaXplfSIsImNhbGxiYWNrQm9keVR5cGUiOiJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQifQ=='
    end

    it 'with extra param' do
      put :callback_string, params: {'x:skip_notify': true}

      expect(assigns(:callback_string)).to eq 'eyJjYWxsYmFja1VybCI6Imh0dHA6Ly9hY2dob3N0LnZpcDo4ODAwL2Rvd25sb2Fkcy9vc3NfY2FsbGJhY2s/IiwiY2FsbGJhY2tCb2R5Ijoib2JqZWN0PSR7b2JqZWN0fVx1MDAyNnNpemU9JHtzaXplfVx1MDAyNng6c2tpcF9ub3RpZnk9dHJ1ZSIsImNhbGxiYWNrQm9keVR5cGUiOiJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWQifQ=='
    end
  end

  describe 'POST #oss_callback' do
    it 'invalid' do
      request.headers["x-oss-pub-key-url"] = Base64.encode64("https://gosspublic.alicdn.com/callback_pub_key_v1.pem")
      request.headers["authorization"] = 'VgB0yfw6uOExzGXI2dazn7zSB+KlUAxeLcfWpvA46TL+qkS9ahtuAUMcHrvCa/LGX+N23BOozR6Fo7oqBVeZQA=='
      post :oss_callback

      expect(response.body).to eq 'Authorization failed!'
    end

    context 'valid' do
      before do
        clear_enqueued_jobs
        allow_any_instance_of(DownloadsController).to receive(:validate_callback).and_return(true)
      end

      it 'valid file size' do
        download.update_columns(is_official: true)
        post :oss_callback, params: {size: 1234, object: [download.id, 'Ch8Tjy3pbZF5PSeadedjQ67Pfa3CQZzE.rar'].join('/')}

        download.reload
        expect(download.permanent_size).to eq 1234
        expect(download.permanent_link).to eq [download.id, 'Ch8Tjy3pbZF5PSeadedjQ67Pfa3CQZzE.rar'].join('/')
        expect(download.price).to eq 10
        expect(enqueued_jobs.size).to eq 1
      end

      it 'exceeding size limit' do
        allow_any_instance_of(Download).to receive(:mb_permanent_size).and_return(700)
        post :oss_callback, params: {size: 1234, object: [download.id, 'Ch8Tjy3pbZF5PSeadedjQ67Pfa3CQZzE.rar'].join('/')}

        download.reload
        expect(enqueued_jobs.size).to eq 1
        expect(download.analysis_stats).to be_nil
      end
    end
  end

  describe 'DELETE #destroy' do
    it 'by normal user' do
      login_user user
      delete :destroy, format: :json, params: {id: download.id}

      expect(response.status).to eq 200
      expect(assigns(:download).skip_refund).to be_truthy
    end

    describe 'valid' do
      before do
        admin = create(:admin)
        login_user admin
      end

      describe 'merit' do
        context 'valid uploader' do
          before do
            allow_any_instance_of(Download).to receive(:should_grant_reward?).and_return(true)
          end

          it 'by admin' do
            delete :destroy, params: {id: download.id}

            Merit::Action.check_unprocessed
            expect(user.points).to eq -5
          end

          it 'do order refund' do
            consumer = create(:user)
            consumer.add_points 10
            order = create(:order, user: consumer, buyable: download, total_amount: 10)

            delete :destroy, params: {id: download.id}
            order.reload
            expect(order.status).to eq 'refunded'
            consumer.reload
            # @note 不再退还购买者积分
            #expect(consumer.points).to eq 10
            # 退回购买者订单8分 + 收回发布资源奖励的5分
            expect(user.points).to eq -5
          end

          it 'skip order refund' do
            consumer = create(:user)
            consumer.add_points 10
            order = create(:order, user: consumer, buyable: download)

            delete :destroy, params: {id: download.id, skip_refund: true, total_amount: 10}  
            order.reload
            expect(order.status).to eq 'refunded'
            consumer.reload
            # 跳过给购买者退款，所以积分不变
            expect(consumer.points).to be_zero
            expect(user.points).to eq -5
          end

          it 'merit category' do
            delete :destroy, params: {id: download.id}

            Merit::Action.check_unprocessed
            user.reload
            category_id = Merit::Score.where(sash_id: user.sash_id, category: 'punishment').first.id

            expect(Merit::Score::Point.where(score_id: category_id).first.num_points).to eq -5
          end
        end

        it 'risk uploader' do
          allow_any_instance_of(User).to receive(:has_upload_risk?).and_return(true)

          delete :destroy, params: {id: download.id}
          # 风险上传者因为上传补丁时没有获得积分，所以删除资源时不与扣除
          expect(user.points).to be_zero
        end
      end
    end

  end
end

/* Dark Theme for Bootstrap V2.3 */

/* 修复图标在黑暗模式下的显示问题 */
html.dark-theme [class^="icon-"],
html.dark-theme [class*=" icon-"] {
  background-image: url("/images/glyphicons-halflings-white.png");
  /* 保持其他属性不变，只修改背景图片 */
  background-repeat: no-repeat;
}

/* 确保 icon-white 类在黑暗模式下仍然使用白色图标 */
html.dark-theme .icon-white,
html.dark-theme .nav-pills > .active > a > [class^="icon-"],
html.dark-theme .nav-pills > .active > a > [class*=" icon-"],
html.dark-theme .nav-list > .active > a > [class^="icon-"],
html.dark-theme .nav-list > .active > a > [class*=" icon-"],
html.dark-theme .navbar-inverse .nav > .active > a > [class^="icon-"],
html.dark-theme .navbar-inverse .nav > .active > a > [class*=" icon-"],
html.dark-theme .dropdown-menu > li > a:hover > [class^="icon-"],
html.dark-theme .dropdown-menu > li > a:focus > [class^="icon-"],
html.dark-theme .dropdown-menu > li > a:hover > [class*=" icon-"],
html.dark-theme .dropdown-menu > li > a:focus > [class*=" icon-"],
html.dark-theme .dropdown-menu > .active > a > [class^="icon-"],
html.dark-theme .dropdown-menu > .active > a > [class*=" icon-"],
html.dark-theme .dropdown-submenu:hover > a > [class^="icon-"],
html.dark-theme .dropdown-submenu:focus > a > [class^="icon-"],
html.dark-theme .dropdown-submenu:hover > a > [class*=" icon-"],
html.dark-theme .dropdown-submenu:focus > a > [class*=" icon-"] {
  background-image: url("/images/glyphicons-halflings-white.png");
}

/* 基础样式 */
html.dark-theme body {
  background-color: #1e1e1e; /* 调整为稍微亮一点的深色 */
  color: #f0f0f0; /* 提高文本亮度以增加对比度 */
}
html.dark-theme legend {
  color: #f0f0f0
}
html.dark-theme blockquote {
  border-left: 5px solid #3a3a3a
}
html.dark-theme pre {
  background-color: transparent;
}
.dropdown-menu:after,
.dropdown-menu:before,
.dropdown-submenu > a:after,
.navbar .dropdown-menu:after,
.navbar .dropdown-menu:before {
  display: none !important;
}

/* 确保所有容器都使用暗色背景 */
html.dark-theme #expired-time .badge-checked {
  background-color: #664d00;
}
html.dark-theme #expired-time .badge-warning {
  background-color: #ff9b08;
}
/* 导航栏 */
html.dark-theme .navbar-fixed-top .navbar-inner,
html.dark-theme .navbar-inner {
  background-color: #252525; /* 调整为稍微亮一点的深色 */
  background-image: none;
  border-color: #3a3a3a;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}
html.dark-theme .nav .active a {
  background-color: #3a3a3a; /* 调整为更明显的活动项背景色 */
  color: #fff
}

/* 确保顶部导航栏下方的区域背景色一致 */
/*
html.dark-theme .site-search,
html.dark-theme .site-name,
html.dark-theme .site-name a {
  background-color: #252525;
  color: #f0f0f0;
}
*/

html.dark-theme .navbar .nav > li > a {
  color: #d8d8d8; /* 提高导航链接的亮度 */
  text-shadow: none;
}

html.dark-theme .navbar .nav > li > a:hover,
html.dark-theme .navbar .nav > li > a:focus {
  color: #fff;
  background-color: #3a3a3a; /* 调整悬停背景色 */
}

html.dark-theme .navbar .nav > .active > a,
html.dark-theme .navbar .nav > .active > a:hover,
html.dark-theme .navbar .nav > .active > a:focus {
  color: #fff;
  background-color: #3a3a3a; /* 保持一致的活动项背景色 */
}

html.dark-theme .navbar .brand {
  color: #eee;
  text-shadow: none;
}

/* 条目评分 */
html.dark-theme .rank-info .star-graphic {
  background: none repeat scroll 0 0 #8A6B58;
}
/* 下拉菜单 */
html.dark-theme .dropdown-menu {
  background-color: #2a2a2a; /* 调整下拉菜单背景色 */
  border: 1px solid #3a3a3a;
}

html.dark-theme .dropdown-menu li > a {
  color: #d8d8d8; /* 提高下拉菜单链接的亮度 */
}

html.dark-theme .dropdown-menu li > a:hover,
html.dark-theme .dropdown-menu li > a:focus {
  background-color: #3a3a3a; /* 调整悬停背景色 */
  background-image: none;
  color: #fff;
}

html.dark-theme .dropdown-menu .divider {
  background-color: #3a3a3a; /* 调整分隔线颜色 */
  border-bottom: 1px solid #4a4a4a;
}

html.dark-theme .dropdown-theme-toggle {
  color: #d8d8d8; /* 提高主题切换按钮文字亮度 */
}

html.dark-theme .theme-toggle-item:hover .dropdown-theme-toggle {
  background-color: #3a3a3a; /* 调整悬停背景色 */
  color: #fff;
}

html.dark-theme .navbar .navbar-inner li.active .dropdown-toggle {
  color: #fff;
  background-color: #3a3a3a; /* 保持一致的活动项背景色 */
}

/* 按钮 */
html.dark-theme .btn {
  background-color: #3a3a3a; /* 调整按钮背景色 */
  background-image: none;
  border-color: #4a4a4a;
  color: #f0f0f0; /* 提高按钮文字亮度 */
  text-shadow: none;
}

html.dark-theme .btn:hover,
html.dark-theme .btn:focus {
  background-color: #4a4a4a; /* 调整悬停背景色 */
  color: #fff;
}

html.dark-theme .btn-primary {
  background-color: #0077dd; /* 调整主要按钮背景色 */
  border-color: #0066cc;
}

html.dark-theme .btn-primary:hover,
html.dark-theme .btn-primary:focus {
  background-color: #0088ee; /* 调整主要按钮悬停背景色 */
}

/* 表单元素 */
html.dark-theme input,
html.dark-theme textarea,
html.dark-theme select,
html.dark-theme .uneditable-input {
  background-color: #333333; /* 调整表单元素背景色 */
  border-color: #4a4a4a;
  color: #f0f0f0; /* 提高表单文字亮度 */
}
html.dark-theme .form-actions {
  background-color: #2a2a2a; /* 调整表单操作区背景色 */
}

html.dark-theme input:focus,
html.dark-theme textarea:focus,
html.dark-theme select:focus {
  border-color: #0088ee; /* 调整焦点边框颜色 */
  box-shadow: 0 0 8px rgba(0, 136, 238, 0.6);
}

/* 搜索栏 */
html.dark-theme .site-search {
  background-color: #252525; /* 调整搜索栏背景色 */
}
/* 广告背景 */
html.dark-theme .adv-couplets,
html.dark-theme #index_bg_box {
  background-color: #1e1e1e !important; /* 调整广告背景色 */
}

html.dark-theme .adv-left,
html.dark-theme .adv-right {
  background-color: transparent !important;
}

/* 区块样式 */
html.dark-theme .block {
  background-color: #252525; /* 调整区块背景色 */
  border-color: #3a3a3a;
}

html.dark-theme .block-header {
  background-color: #2a2a2a; /* 调整区块头部背景色 */
  border-color: #3a3a3a;
}

html.dark-theme .well {
  background-color: #252525; /* 调整well背景色 */
}
html.dark-theme .caption {
  color: inherit
}
/* 表格 */
html.dark-theme .table th,
html.dark-theme .table td {
  border-color: #3a3a3a; /* 调整表格边框颜色 */
}

html.dark-theme .table-striped tbody > tr:nth-child(odd) > td,
html.dark-theme .table-striped tbody > tr:nth-child(odd) > th {
  background-color: #2a2a2a; /* 调整表格条纹背景色 */
}

html.dark-theme .table-hover tbody tr:hover > td,
html.dark-theme .table-hover tbody tr:hover > th {
  background-color: #333333; /* 调整表格悬停背景色 */
}

/* 标签和徽章 */
html.dark-theme .label,
html.dark-theme .badge {
  text-shadow: none;
  background-color: #3a3a3a; /* 调整标签背景色 */
}

/* 徽章特殊样式 */
html.dark-theme .badge {
  color: #ffffff !important; /* 确保徽章文字颜色为白色，提高可读性 */
  background-color: #0077dd; /* 调整徽章背景色为主题蓝色 */
}

/* 搜索结果标签 */
html.dark-theme .search-result-tags .result-tag a,
html.dark-theme #content .search-result-tags .result-tag a {
  color: #ffffff !important; /* 确保标签文字颜色为白色 */
  background-color: transparent;
}

html.dark-theme .search-result-tags .result-tag {
  background-color: #3a3a3a; /* 调整搜索结果标签背景色 */
  border-color: #4a4a4a;
}

/* 面包屑导航 */
html.dark-theme .breadcrumb {
  background-color: #252525; /* 调整面包屑背景色 */
}

html.dark-theme .breadcrumb > li {
  text-shadow: none;
  color: #d8d8d8; /* 提高面包屑文字亮度 */
}

html.dark-theme .breadcrumb > .active {
  color: #aaaaaa; /* 调整活动项文字颜色 */
}

/* 分页 */
html.dark-theme .pagination ul > li > a,
html.dark-theme .pagination ul > li > span {
  background-color: #2a2a2a; /* 调整分页背景色 */
  border-color: #3a3a3a;
  color: #d8d8d8; /* 提高分页文字亮度 */
}

html.dark-theme .pagination ul > li > a:hover,
html.dark-theme .pagination ul > li > a:focus {
  background-color: #3a3a3a; /* 调整分页悬停背景色 */
  color: #fff;
}

html.dark-theme .pagination ul > .active > a,
html.dark-theme .pagination ul > .active > span {
  background-color: #0077dd; /* 调整分页活动项背景色 */
  color: #fff;
}

/* 警告框 */
html.dark-theme .alert {
  background-color: #333333; /* 调整警告框背景色 */
  border-color: #4a4a4a;
  color: #f0f0f0; /* 提高警告框文字亮度 */
  text-shadow: none;
}

/* 私信对话中发件人消息的特殊样式 */
html.dark-theme .alert-info {
  background-color: #1a4a6b; /* 深蓝色背景，区别于收件人消息 */
  border-color: #2a5a7b;
  color: #e6f3ff; /* 稍微亮一点的文字颜色 */
}

/* 评论区 */
html.dark-theme .comments .media {
  border-color: #3a3a3a; /* 调整评论区边框颜色 */
}

html.dark-theme .comments .popular {
  background-color: #2a2a2a; /* 调整热门评论背景色 */
}

html.dark-theme .comments .media .content {
  background-color: #252525; /* 调整评论内容背景色 */
}

/* 链接颜色 */
html.dark-theme a {
  color: #5db1ff; /* 调整链接颜色为更亮的蓝色 */
}

html.dark-theme a:hover,
html.dark-theme a:focus {
  color: #7fc1ff; /* 调整链接悬停颜色 */
  background-color: transparent;
  text-decoration: underline; /* 添加下划线以提高可读性 */
}

/* 确保特定链接样式 */
html.dark-theme .btn a,
html.dark-theme .badge a,
html.dark-theme .label a {
  color: #ffffff;
  text-decoration: none; /* 按钮和标签中的链接不需要下划线 */
}

/* 游戏列表样式 */
html.dark-theme .intro-list .media,
html.dark-theme .download-list .media,
html.dark-theme .subject-index-list li {
  border-color: #3a3a3a; /* 调整列表边框颜色 */
}

html.dark-theme .media {
  background-color: #252525; /* 调整媒体项背景色 */
}
html.dark-theme .inbox .media-list .active .media {
  background-color: #0077dd; /* 调整活动媒体项背景色 */
}
/* 确保文本颜色 */
html.dark-theme .help-block, .help-inline {
  color: #999;
}

html.dark-theme .text-success {
  color: #6cd86c; /* 调整成功文本颜色 */
}

html.dark-theme .text-error {
  color: #ff6b6b; /* 调整错误文本颜色 */
}

html.dark-theme .text-info {
  color: #6dcaee; /* 调整信息文本颜色 */
}

html.dark-theme .text-warning {
  color: #ffbe5c; /* 调整警告文本颜色 */
}
html.dark-theme .diff del, html.dark-theme .diff ins {
  color: #252525
}

/* Panel 布局特定样式 */
html.dark-theme .panel-header {
  background-color: #252525; /* 调整面板头部背景色 */
  color: #f0f0f0; /* 提高面板头部文字亮度 */
}

html.dark-theme .panel-header .nav-pills > li > a {
  color: #d8d8d8; /* 提高导航链接亮度 */
}

html.dark-theme .panel-header .nav-pills > li > a:hover,
html.dark-theme .panel-header .nav-pills > li > a:focus {
  background-color: #3a3a3a; /* 调整悬停背景色 */
  color: #fff;
}

html.dark-theme .panel-header .nav-pills > .active > a,
html.dark-theme .panel-header .nav-pills > .active > a:hover,
html.dark-theme .panel-header .nav-pills > .active > a:focus {
  background-color: #0077dd; /* 调整活动项背景色 */
  color: #fff;
}

html.dark-theme .panel-body {
  background-color: #252525; /* 调整面板主体背景色 */
}

html.dark-theme .panel-list {
  border-color: #3a3a3a; /* 调整面板列表边框颜色 */
}

html.dark-theme .inbox div.span3,
html.dark-theme .inbox div.input-append {
  background-color: #252525; /* 调整收件箱背景色 */
}

html.dark-theme .conversation {
  background-color: #252525; /* 调整对话背景色 */
  border-color: #3a3a3a;
}

html.dark-theme .inbox .contact a.load_dialogue:hover {
  background-color: #3a3a3a; /* 调整对话悬停背景色 */
}

html.dark-theme .inbox .media-list li.active {
  background-color: #0077dd; /* 调整活动项背景色 */
}

html.dark-theme .inbox .media-list li.active a.load_dialogue:hover {
  background-color: #0088ee; /* 调整活动项悬停背景色 */
}

/* 特定的 panel 元素样式 */
html.dark-theme .favorites li.even {
  background-color: #2a2a2a; /* 调整偶数项背景色 */
}

html.dark-theme .cycle-start {
  background-color: #2a2a2a; /* 调整循环开始背景色 */
  border-color: #5cb85c; /* 调整边框颜色 */
}

html.dark-theme .order-list,
html.dark-theme .panel-list {
  background-color: #252525; /* 调整列表背景色 */
}

html.dark-theme .order-list th,
html.dark-theme .panel-list th {
  background-color: #2a2a2a; /* 调整表头背景色 */
}

html.dark-theme .order-list td,
html.dark-theme .panel-list td {
  border-color: #3a3a3a; /* 调整单元格边框颜色 */
}

/* 确保表单元素在 panel 中的样式 */
html.dark-theme .panel-body input,
html.dark-theme .panel-body textarea,
html.dark-theme .panel-body select {
  background-color: #333333; /* 调整表单元素背景色 */
  border-color: #4a4a4a;
  color: #f0f0f0; /* 提高表单文字亮度 */
}

html.dark-theme .panel-body input:focus,
html.dark-theme .panel-body textarea:focus,
html.dark-theme .panel-body select:focus {
  border-color: #0088ee; /* 调整焦点边框颜色 */
  box-shadow: 0 0 8px rgba(0, 136, 238, 0.6);
}

/* Zabuto Calendar 黑暗主题样式 */
html.dark-theme div.zabuto_calendar {
  color: #f0f0f0;
}

html.dark-theme div.zabuto_calendar .table tr th,
html.dark-theme div.zabuto_calendar .table tr td {
  background-color: #252525;
  border-color: #3a3a3a;
  color: #f0f0f0;
}

html.dark-theme div.zabuto_calendar .table tr:last-child {
  border-bottom: 1px solid #3a3a3a;
}

html.dark-theme div.zabuto_calendar .table tr.calendar-month-header td {
  background-color: #2a2a2a;
  color: #f0f0f0;
}

html.dark-theme div.zabuto_calendar .table tr.calendar-dow-header th {
  background-color: #333333;
  color: #d8d8d8;
}

/* 日期单元格 */
html.dark-theme div.zabuto_calendar .table tr td div.day {
  color: #f0f0f0;
}

/* 今天的日期 */
html.dark-theme div.zabuto_calendar .badge-today,
html.dark-theme div.zabuto_calendar div.legend span.badge-today {
  background-color: #0077dd;
  color: #fff;
}

/* 事件日期 */
html.dark-theme div.zabuto_calendar .table tr td.event div.day,
html.dark-theme div.zabuto_calendar ul.legend li.event {
  background-color: #3a3a3a;
}

/* 已签到日期 - 使用更明显的颜色 */
html.dark-theme div.zabuto_calendar .table tr td.event div.day,
html.dark-theme div.zabuto_calendar ul.legend li.event {
  background-color: #664d00;
}

/* 可补签日期 */
html.dark-theme div.zabuto_calendar .badge-event,
html.dark-theme div.zabuto_calendar div.legend span.badge-event {
  background-color: #ff9b08;
  color: #fff;
}

/* 当前签到周期的起始日 */
html.dark-theme .cycle-start {
  background-color: #1e4d1e;
  border-color: #5cb85c;
}

/* 导航按钮 */
html.dark-theme div.zabuto_calendar div.calendar-month-navigation {
  color: #f0f0f0;
}

/* 图例 */
html.dark-theme div.zabuto_calendar div.legend {
  color: #d8d8d8;
}

html.dark-theme div.zabuto_calendar div.legend span {
  color: #d8d8d8;
}

/* 日历标记说明 */
html.dark-theme #expired-time li {
  color: #f0f0f0;
}

html.dark-theme #expired-time .badge {
  color: #fff;
}

/* 通知 */
html.dark-theme .notification span.description {
  color: #e0e0e0;
}

html.dark-theme .notification .media-body {
  color: #f0f0f0;
}

html.dark-theme .notification .media-list li {
  border-bottom: 1px solid #3a3a3a;
}

html.dark-theme .notification .muted {
  color: #aaaaaa;
}

html.dark-theme .notification a.item {
  color: #5db1ff;
  text-decoration: none;
}

html.dark-theme .notification a.item:hover {
  color: #7fc1ff;
}

/* 鼠标悬停效果 */
html.dark-theme div.zabuto_calendar .table tr td.dow-clickable:hover,
html.dark-theme div.zabuto_calendar .table tr td.event-clickable:hover {
  background-color: #3a3a3a;
}

/* 页脚 */
html.dark-theme hr {
  border-bottom: none;
  border-top: 1px solid #3a3a3a; /* 调整分隔线颜色 */
}

/* 模态框 */
html.dark-theme .modal {
  background-color: #252525; /* 调整模态框背景色 */
  border: 1px solid #3a3a3a;
}

html.dark-theme .modal-header {
  border-bottom: 1px solid #3a3a3a; /* 调整模态框头部边框颜色 */
  color: #f0f0f0; /* 确保标题文字颜色一致 */
}

html.dark-theme .modal-header h4 {
  color: #f0f0f0; /* 确保标题文字颜色一致 */
}

html.dark-theme .modal-body {
  color: #f0f0f0; /* 确保内容文字颜色一致 */
}

html.dark-theme .modal-footer {
  background-color: #2a2a2a; /* 调整模态框底部背景色 */
  border-top: 1px solid #3a3a3a;
  box-shadow: none;
}

/* 弹出层 */
html.dark-theme .popover {
  background-color: #252525; /* 调整弹出层背景色 */
  border: 1px solid #3a3a3a;
  color: #f0f0f0; /* 确保弹出层文字颜色一致 */
}

html.dark-theme .popover-title {
  background-color: #2a2a2a; /* 调整弹出层标题背景色 */
  border-bottom: 1px solid #3a3a3a;
  color: #f0f0f0; /* 确保弹出层标题文字颜色一致 */
}

html.dark-theme .popover-content {
  color: #f0f0f0; /* 确保弹出层内容文字颜色一致 */
}

html.dark-theme .popover-content p {
  color: #f0f0f0; /* 确保弹出层段落文字颜色一致 */
}

html.dark-theme .popover-content a {
  color: #5db1ff; /* 确保弹出层链接颜色一致 */
}

html.dark-theme .popover-content a:hover,
html.dark-theme .popover-content a:focus {
  color: #7fc1ff; /* 确保弹出层链接悬停颜色一致 */
  background-color: transparent;
  text-decoration: underline;
}

html.dark-theme .popover.top .arrow {
  border-top-color: #3a3a3a; /* 调整弹出层箭头边框颜色 */
}

html.dark-theme .popover.right .arrow {
  border-right-color: #3a3a3a; /* 调整弹出层箭头边框颜色 */
}

html.dark-theme .popover.bottom .arrow {
  border-bottom-color: #3a3a3a; /* 调整弹出层箭头边框颜色 */
}

html.dark-theme .popover.left .arrow {
  border-left-color: #3a3a3a; /* 调整弹出层箭头边框颜色 */
}

html.dark-theme .popover.top .arrow:after {
  border-top-color: #252525; /* 调整弹出层箭头颜色 */
}

html.dark-theme .popover.right .arrow:after {
  border-right-color: #252525; /* 调整弹出层箭头颜色 */
}

html.dark-theme .popover.bottom .arrow:after {
  border-bottom-color: #252525; /* 调整弹出层箭头颜色 */
}

html.dark-theme .popover.left .arrow:after {
  border-left-color: #252525; /* 调整弹出层箭头颜色 */
}

/* 通知模态框特定样式 */
html.dark-theme #last_notifications {
  background-color: #252525; /* 确保通知模态框背景色一致 */
  border: 1px solid #3a3a3a;
}

html.dark-theme #last_notifications .modal-header {
  background-color: #2a2a2a; /* 确保通知模态框头部背景色一致 */
  color: #f0f0f0;
}

html.dark-theme #last_notifications .modal-body {
  background-color: #252525; /* 确保通知模态框内容背景色一致 */
}

html.dark-theme #last_notifications .modal-footer {
  background-color: #2a2a2a; /* 确保通知模态框底部背景色一致 */
}

html.dark-theme #last_notifications .media-list li {
  border-bottom: 1px solid #3a3a3a;
}

html.dark-theme #last_notifications .media-list li:hover {
  background-color: #333333; /* 调整悬停背景色 */
}

html.dark-theme #last_notifications .modal-body a {
  color: #5db1ff; /* 确保链接颜色一致 */
  text-decoration: none;
}

html.dark-theme #last_notifications .media-body {
  color: #f0f0f0; /* 确保文字颜色一致 */
}

html.dark-theme #last_notifications .muted {
  color: #aaaaaa; /* 确保静音文字颜色一致 */
}

html.dark-theme #last_notifications .text-center.muted {
  color: #aaaaaa; /* 确保无通知提示文字颜色一致 */
  padding: 15px;
}

/* CKEditor 黑暗模式样式 */
/* CKEditor mentions 插件下拉框样式 */
html.dark-theme .cke_autocomplete_panel {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
}

html.dark-theme .cke_autocomplete_panel ul {
  background-color: #2a2a2a !important;
  border: none !important;
}

html.dark-theme .cke_autocomplete_panel li {
  background-color: #2a2a2a !important;
  color: #d8d8d8 !important;
  border-bottom: 1px solid #3a3a3a !important;
}

html.dark-theme .cke_autocomplete_panel li:hover,
html.dark-theme .cke_autocomplete_panel li.cke_autocomplete_selected {
  background-color: #3a3a3a !important;
  color: #fff !important;
}

html.dark-theme .cke_autocomplete_panel li a {
  color: #d8d8d8 !important;
  text-decoration: none !important;
}

html.dark-theme .cke_autocomplete_panel li:hover a,
html.dark-theme .cke_autocomplete_panel li.cke_autocomplete_selected a {
  color: #fff !important;
}

/* CKEditor mentions 插件的其他可能的类名 */
html.dark-theme .cke_panel_block {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
}

html.dark-theme .cke_panel_block ul {
  background-color: #2a2a2a !important;
}

html.dark-theme .cke_panel_block li {
  background-color: #2a2a2a !important;
  color: #d8d8d8 !important;
}

html.dark-theme .cke_panel_block li:hover,
html.dark-theme .cke_panel_block li.cke_selected {
  background-color: #3a3a3a !important;
  color: #fff !important;
}

html.dark-theme .cke_panel_block li a {
  color: #d8d8d8 !important;
}

html.dark-theme .cke_panel_block li:hover a,
html.dark-theme .cke_panel_block li.cke_selected a {
  color: #fff !important;
}

/* CKEditor 通用面板样式 */
html.dark-theme .cke_panel {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
}

html.dark-theme .cke_menu {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
}

html.dark-theme .cke_menuitem {
  background-color: #2a2a2a !important;
  color: #d8d8d8 !important;
}

html.dark-theme .cke_menuitem:hover,
html.dark-theme .cke_menuitem_selected {
  background-color: #3a3a3a !important;
  color: #fff !important;
}

html.dark-theme .cke_menuitem a {
  color: #d8d8d8 !important;
}

html.dark-theme .cke_menuitem:hover a,
html.dark-theme .cke_menuitem_selected a {
  color: #fff !important;
}

/* CKEditor mentions 插件的额外样式覆盖 */
html.dark-theme .cke_autocomplete,
html.dark-theme .cke_autocomplete_panel,
html.dark-theme .cke_autocomplete_list {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
  color: #d8d8d8 !important;
}

html.dark-theme .cke_autocomplete_item,
html.dark-theme .cke_autocomplete_list li {
  background-color: #2a2a2a !important;
  color: #d8d8d8 !important;
  border-bottom: 1px solid #3a3a3a !important;
  padding: 5px 10px !important;
}

html.dark-theme .cke_autocomplete_item:hover,
html.dark-theme .cke_autocomplete_item.selected,
html.dark-theme .cke_autocomplete_list li:hover,
html.dark-theme .cke_autocomplete_list li.selected {
  background-color: #3a3a3a !important;
  color: #fff !important;
}

/* 针对可能的iframe内容样式 */
html.dark-theme iframe.cke_wysiwyg_frame {
  background-color: #2d2d2d !important;
}

/* 确保CKEditor内部的下拉框也适配黑暗模式 */
html.dark-theme .cke_combo_open .cke_combo_arrow {
  border-top-color: #d8d8d8 !important;
}

html.dark-theme .cke_combo_text {
  color: #d8d8d8 !important;
}

html.dark-theme .cke_combo:hover .cke_combo_text {
  color: #fff !important;
}

/* 覆盖任何可能的白色背景的下拉框 */
html.dark-theme div[style*="background-color: white"],
html.dark-theme div[style*="background-color: #ffffff"],
html.dark-theme div[style*="background-color: #fff"],
html.dark-theme div[style*="background: white"],
html.dark-theme div[style*="background: #ffffff"],
html.dark-theme div[style*="background: #fff"] {
  background-color: #2a2a2a !important;
  color: #d8d8d8 !important;
}

/* 确保下拉框中的文本颜色正确 */
html.dark-theme div[style*="background-color: white"] *,
html.dark-theme div[style*="background-color: #ffffff"] *,
html.dark-theme div[style*="background-color: #fff"] *,
html.dark-theme div[style*="background: white"] *,
html.dark-theme div[style*="background: #ffffff"] *,
html.dark-theme div[style*="background: #fff"] * {
  color: #d8d8d8 !important;
}

/* 针对CKEditor的特定mentions样式 */
html.dark-theme .cke_mentions_panel {
  background-color: #2a2a2a !important;
  border: 1px solid #3a3a3a !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
}

html.dark-theme .cke_mentions_panel ul {
  background-color: #2a2a2a !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.dark-theme .cke_mentions_panel li {
  background-color: #2a2a2a !important;
  color: #d8d8d8 !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid #3a3a3a !important;
  cursor: pointer !important;
}

html.dark-theme .cke_mentions_panel li:hover,
html.dark-theme .cke_mentions_panel li.cke_mentions_selected {
  background-color: #3a3a3a !important;
  color: #fff !important;
}

/* 主题切换开关样式 */
.theme-switch {
  display: inline-block;
  position: relative;
  width: 60px;
  height: 24px;
  margin-top: 8px;
}

/* 当在导航栏中时的样式 */
.nav > li > .theme-switch {
  margin-top: 8px;
  margin-bottom: 8px;
}

/* 当在下拉菜单中时的样式 */
.dropdown-theme-toggle {
  padding: 3px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dropdown-theme-toggle span {
  margin-right: 10px;
}

.dropdown-theme-toggle .theme-switch {
  margin-top: 0;
  margin-bottom: 0;
}

.theme-toggle-item {
  min-height: 20px;
}

.theme-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: -8px;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.theme-switch .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.theme-switch input:checked + .slider {
  background-color: #0077dd; /* 调整开关背景色为主题蓝色 */
}

.theme-switch input:checked + .slider:before {
  transform: translateX(36px);
}

.theme-switch .slider .icon {
  position: absolute;
  top: 4px;
  font-size: 16px;
  color: #fff;
}

.theme-switch .slider .icon-sun {
  left: 6px;
  display: none;
}

.theme-switch .slider .icon-moon {
  right: 6px;
}

.theme-switch input:checked + .slider .icon-sun {
  display: block;
}

.theme-switch input:checked + .slider .icon-moon {
  display: none;
}

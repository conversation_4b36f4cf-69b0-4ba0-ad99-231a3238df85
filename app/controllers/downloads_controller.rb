class DownloadsController < ApplicationController
  include SorcerySharedActions
  include CommentsSharedActions
  require 'aliyun/oss'
  require 'aliyun/sts'

  skip_before_action :verify_authenticity_token, only: [:oss_callback]
  before_action :require_login, except: [:index, :show, :oss_callback, :comments]
  before_action :set_download, only: [:show, :edit, :update, :destroy, :audits]
  before_action :set_subject, only: [:new]
  before_action :load_oss_setting, only: [:new, :edit, :audits]
  before_action :redirect_to_parent, only: [:show, :edit]
  before_action :validate_callback, only: [:oss_callback]
  load_and_authorize_resource

  # GET /downloads
  # GET /downloads.json
  def index
    @downloads = Download.censored(@censor_level).page(params[:page]).order(created_at: :desc)

    @hots = Download.censored(@censor_level).joins(:subject).where('downloads.created_at between ? and ?', 1.weeks.ago, Time.now).order('subjects.comments_count desc').limit(10)

    set_seo_meta "下载资源列表"

    if params[:kind].present?
      @downloads = @downloads.where(kind: Download.kinds[params[:kind].to_sym])
      @hots = @hots.where(kind: Download.kinds[params[:kind].to_sym])

      kind_name = I18n.t("enums.download.kind.#{params[:kind]}", default: '')
      set_seo_meta "#{kind_name}_下载资源列表"
    end

    if params[:subject_id].present?
      @downloads = @downloads.where(subject_id: params[:subject_id])
      @subject = Subject.unscoped.find(params[:subject_id])
      render_no_found and return unless @subject.deleted_at.nil?

      set_seo_meta "#{@subject.name}的下载资源"
    end

    if params.has_key?(:user_id)
      redirect_to :not_authenticated_users and return unless logged_in?

      @user = User.find(params[:user_id])
      @count = @downloads.where(user_id: params[:user_id]).total_count
      @downloads = @downloads.where(user_id: params[:user_id])

      if current_user.id == @user.id
        set_seo_meta "我的下载资源"
      else
        set_seo_meta "#{@user.name}的下载资源"
      end
      render layout: 'panel', template: 'downloads/panel'
    end
  end

  # GET /downloads/1
  # GET /downloads/1.json
  def show
    render_no_found and return unless @censor_level.include?(Subject.censors[@download.subject.censor])

    @related_topics = @download.subject.topics.valid.where(type: ['Walkthrough', 'Review'])
    @related_downloads = @download.subject.downloads.where.not(id:  @download.id)
    page = @download.comments_last_page(ignore_spam: true)

    # 评论
    load_comments(@download, page, {is_spam: false})

    @spams = load_parent_comments(@download, {is_spam: true}).limit(100)
    @spams_children = load_children_comments(@spams)

    @hot_comments = Digg.select(:comment_id).joins(:comment).where('reward > 10').where('comments.commentable_type = \'Download\' and comments.commentable_id = ?', @download.id).group(:comment_id).order('sum(reward) desc').limit(3).map(&:comment)
    dug_comment_ids = @comments.map(&:id) | @hot_comments.map(&:id) | @comments.collect{|comment| comment.children.pluck(:id)}

    @dug_ids = Digg.dug_by(current_user, dug_comment_ids.flatten)
    @comment = Comment.new(commentable_id: @download.id, commentable_type: 'Download')

    if @download.permanent_link.present?
      @order = current_user.orders.where(buyable: @download).first if logged_in?
      @price = Order.new(user: current_user, buyable: @download).sale_price if @order.nil?
      oss = Oss.new @download
      @bucket = oss.client.get_bucket(oss.bucket_name)
    end

    # 如果为机翻补丁，判断所属资源是否存在汉化补丁
    @has_human_trans = Download.where(subject: @download.subject, kind: 'human_trans').count.positive? if ['machine_trans', 'ai_trans'].include?(@download.kind)

    @activity = Activity.only_deleted.where(pushable: @download).first if @download.created_at > 7.days.ago
    # 上传未完成的也禁止下载
    @activity = Activity.new if @download.permanent_link == '/json_upload/unfinished'

    description = [@download.title]
    keywords = @download.subject.aka_list | description
    set_seo_meta @download.title, keywords.join, description.join
  end

  # GET /downloads/new
  def new
    @download = Download.new(user: current_user)
    @title = t('views.new_download', subject: @subject.name)
    @can_allow_local_upload = current_user.can_use_local_store?

    set_seo_meta @title
  end

  # GET /downloads/1/edit
  def edit
    @download = Download.find(params[:id])
    @download.url = @download.url.join(', ') if @download.url.respond_to?(:join)
    @subject = @download.subject
    @title = t('views.edit_download', title: @download.title)

    set_seo_meta @title
    render template: 'downloads/new'
  end

  # 获取oss指定资源的版本列表
  def audits
    objects = @bucket.list_objects(prefix: "#{@download.id}/", limit: 5)

    @objects = objects.sort_by{|obj| obj.last_modified}.reverse
  end

  def oss_callback
    id = params[:object].split('/').first
    download = Download.find(id)
    download.update(manual_price: download.price, permanent_link: params[:object], permanent_size: params[:size])

    #if download.mb_permanent_size < Oss::SIZE_LIMIT
    # @note 普通会员oss迁移到web服后，所有文件都需要反向同步回web服务器
    OssToLocalJob.set(wait: 1.seconds).perform_later download, params[:'x:skip_notify']
    #else
    #download.update(analysis_stats: {})
    #end

    render json: { "String value": "ok", "Key": "Status"}, status: :ok
  end

  def callback_string
    aliyun_config = Oss.config
    body = {
      size: '${size}',
      object: '${object}'
    }.merge!(callback_params)

    callback = Aliyun::OSS::Callback.new( url: aliyun_config[:callback_url], body: CGI.unescape(body.to_query))
    @callback_string = callback.serialize

    render json: {callback_string: @callback_string}
  end

  # POST /downloads
  # POST /downloads.json
  def create
    @download = Download.new(download_params)
    @download.user_id = current_user.id
    @download.permanent_link = '/json_upload/unfinished' if params.key?(:pre_upload)

    respond_to do |format|
      begin
        if @download.save
          format.html { redirect_to @download, notice: 'Download was successfully created.' }
          format.json { render json: {id: @download.id} }
        else
          #flash[:error] = @download.errors.full_messages
          #@subject = @download.subject
          #format.html { render :new, status: :unprocessable_entity}
          format.json { render json: {message: @download.errors.full_messages, success: false}, status: :unprocessable_entity}
        end
      rescue CarrierWave::IntegrityError => e
        render json: {message: e.message, success: false}, status: :unprocessable_entity and return
      end
    end
  end

  rescue_from CanCan::AccessDenied do |exception|
    @message = exception.message
    respond_to do |format|
      format.json { render json: {message: [@message], success: false}, status: :forbidden }
      format.html { render :forbidden, layout: 'login'}
      format.js { render json: {message: [@message], success: false}, status: :forbidden }
    end
  end

  # PATCH/PUT /downloads/1
  # PATCH/PUT /downloads/1.json
  def update
    @subject = @download.subject

    respond_to do |format|
      if @download.update(download_params)
        format.html { redirect_to @download, notice: 'Download was successfully updated.' }
        format.json { render :show, status: :ok, location: @download }
      else
        flash[:error] = @download.errors.full_messages
        format.html { render :new }
        format.json { render json: @download.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @user = @download.user
    @user.subtract_points(5, category: 'punishment') if @download.should_grant_reward?
    @download.skip_refund = true if params[:skip_refund].present? || !current_user.admin?
    @download.operator = current_user
    @download.destroy
    render json: {message: "ok", success: true}, status: :ok
  end

  private
  def get_header(name)
    key = "http_#{name.gsub('-', '_')}".upcase
    request.env[key]
  end

  PUB_KEY_URL_PREFIX = 'http://gosspublic.alicdn.com/'
  PUB_KEY_URL_PREFIX_S = 'https://gosspublic.alicdn.com/'

  def get_public_key(pub_key_url, reload = false)
    render plain: "Invalid public key URL: #{pub_key_url}", status: 400 and return unless pub_key_url.start_with?(PUB_KEY_URL_PREFIX) || pub_key_url.start_with?(PUB_KEY_URL_PREFIX_S)

    if reload || @pub_key.nil?
      # https://stackoverflow.com/questions/65937714/no-such-file-or-directory-rb-sysopen-for-external-url-rails-6-11-ruby-3
      @pub_key = URI.open(pub_key_url) { |f| f.read }
    end

    @pub_key
  end

  def redirect_to_parent
    redirect_to download_path(@download.parent_id), status: :moved_permanently unless @download.parent_id.nil?
  end

  def validate_callback
    pub_key_url = Base64.decode64(get_header('x-oss-pub-key-url'))
    pub_key = get_public_key(pub_key_url)
    rsa = OpenSSL::PKey::RSA.new(pub_key)

    authorization = Base64.decode64(get_header('authorization'))
    req_body = request.body.read

    auth_str = if request.query_string.empty?
                 CGI.unescape(request.path) + "\n" + req_body
               else
                 CGI.unescape(request.path) + '?' + request.query_string + "\n" + req_body
               end

    valid = rsa.public_key.verify(OpenSSL::Digest::MD5.new, authorization, auth_str)
    render plain: "Authorization failed!", status: 400 and return unless valid
  end

  def set_download
    begin 
      @download = Download.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      if logged_in?
        current_user.error_download_ids << params[:id]  && controller_name == 'downloads'
        current_user.add_role :boter, Download if current_user.error_download_ids.size > 7
      end
      render_no_found and return
    end
  end

  def load_oss_setting
    @can_allow_oss_upload = current_user.can_use_local_store?
    @can_allow_local_upload = @can_allow_oss_upload

    # @todo 改造这部分逻辑，使用Oss封装
    if @can_allow_oss_upload
      aliyun_config = Oss.config
      sts = Aliyun::STS::Client.new( access_key_id: aliyun_config[:key],  access_key_secret: aliyun_config[:secret])

      @token = sts.assume_role(aliyun_config[:arn], 'uploader')
      client = Aliyun::OSS::Client.new( endpoint: aliyun_config[:host], cname: true, :access_key_id => @token.access_key_id, :access_key_secret => @token.access_key_secret, :sts_token => @token.security_token)
      @bucket = client.get_bucket(aliyun_config[:bucket])

      callback = Aliyun::OSS::Callback.new( url: aliyun_config[:callback_url], body: 'size=${size}&object=${object}')
      @callback_string = callback.serialize

      expiry = 15.minutes
      policy_hash = {
        'expiration': (Time.now + expiry).utc.iso8601.sub('Z', '.000Z'),
        'conditions': [{'bucket': @bucket.name}]
      }
      @policy = Base64.strict_encode64(policy_hash.to_json)
    end
  end

  def set_subject
    redirect_to downloads_path and return if params[:subject_id].blank?
    @subject = Subject.find(params[:subject_id])

    render_no_found and return unless @censor_level.include?(Subject.censors[@subject.censor])
  end

  def download_params
    permit_list = [:title, :description, :subject_id, :url, :file, :kind, :price, :skip_notify]
    permit_list << :is_official unless current_user.risk_uploader?
    permit_list << :manual_price if action_name == 'create'
    if current_user.admin?
      permit_list << :permanent_link unless params.dig(:download, :permanent_link).blank?
      permit_list << :parent_id
    end
    params.require(:download).permit(permit_list)
  end

  def callback_params
    params.permit(:'x:skip_notify')
  end
end

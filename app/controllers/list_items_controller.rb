class ListItemsController < ApplicationController
  include SorcerySharedActions

  before_action :require_login, except: [:index]
  before_action :set_list, only: [:create, :index]
  before_action :set_list_item, only: [:edit, :update, :destroy]
  before_action :set_lists, only: [:new]
  load_and_authorize_resource

  def index
    keyword = params[:list_keyword].blank? ? '*' : params[:list_keyword]

    @list_items = ListItem.search(keyword, where: {list_id: @list.id}, fields: [{"name^5": :word_middle}, {"comment": :word_middle}, "tags^2", "synonyms^2"], order: order_hash, page: params[:page], per_page: params[:per_page] || List::default_per_page)

    respond_to do |format|
      format.json { render json: { list_items: render_to_string('list_items/_list', layout: false, formats: [:html], locals: { items: @list_items})} }
    end
  end

  # 向列表中添加一个条目
  def create
    @subject_ids = parse_subject_ids_from_param
    @created_items = []
    @failed_items = []

    @subject_ids.each do |subject_id|
      list_item = ListItem.new(subject_id: subject_id, list_id: @list.id, comment: list_item_params[:comment], weight: list_item_params[:weight])
      if list_item.save
        @created_items << list_item
      else
        @failed_items << { subject_id: subject_id, errors: list_item.errors.full_messages }
      end
    end

    respond_to do |format|
      if @failed_items.empty? || @created_items.present?
        # 有成功添加的条目
        @subjects = Subject.where(id: @created_items.map(&:subject_id))
        @subjects_hash = @subjects.index_by(&:id)

        # 获取评分信息
        ranks = @list.user.ranks.where(subject_id: @subjects.pluck(:id))
        @scores = ranks.each_with_object({}) do |rank, hash|
          hash[rank.subject_id] = Rank.scores[rank.score.to_sym] if rank.score
        end

        @list_items_string = render_to_string('list_items/_batch_items', layout: false, formats: [:html], locals: { list_items: @created_items, subjects_hash: @subjects_hash, scores: @scores })

        # 清除之前的错误消息
        flash[:error] = nil

        format.html { redirect_to @list, notice: 'Subject was successfully created.' }
        format.json {
          render json: {
            message: "ok",
            success: true,
            items: @list_items_string,
            failed_count: @failed_items.size
          }
        }
      else
        # 全部添加失败
        flash[:error] = @failed_items.map { |item| item[:errors] }.flatten.uniq

        format.html { render :new }
        format.json { render json: { message: flash[:error], success: false } }
      end
    end
  end

  def edit
  end

  def new
    redirect_to new_list_path if @lists.blank?
    @subject = Subject.find(params[:subject_id])
    @list_item = ListItem.new(subject_id: @subject.id)

    @title = t('views.add_item_to_list')

    set_seo_meta @title
  end

  def update
    respond_to do |format|
      if @list_item.update(list_item_params)
        @subject = @list_item.subject

        format.html { redirect_to @list_item, notice: 'ListItem was successfully updated.' }
        format.json { render :show, status: :ok, location: @list_item }
      else
        format.html { render :edit }
        format.json { render json: @list_item.errors, status: :unprocessable_entity }
      end
    end
  end

  # 从列表中移除一个条目
  def destroy
    @list_item.destroy

    render json: {message: "ok", success: true}, status: :ok
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_list_item
      @list_item = ListItem.find(params[:id])
    end

    def list_item_params
      params.require(:list_item).permit(:subject_id, :comment, :weight)
    end

    def set_list
      @list = List.where(id: params[:list_id]).first
    end

    def set_lists
      @lists = current_user.lists.inject({}) do |hash, list|
        hash[list.name] = list.id
        hash
      end
    end

    def order_hash
       case params[:order]
       when 'score', 'released_at', 'weight'
        Hash[params[:order].to_sym, :desc]
       else
         {created_at: :desc}
       end
    end

    def current_ability
      @list ||=  @list_item.try(:list)
      @current_ability ||= Ability.new(current_user, @list)
    end

    # 从参数中解析subject_ids
    def parse_subject_ids_from_param
      return [] if params[:subject_ids].blank?

      begin
        JSON.parse(params[:subject_ids])
      rescue JSON::ParserError
        []
      end
    end
end

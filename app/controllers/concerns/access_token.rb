module AccessToken
  extend ActiveSupport::Concern

  def current_token
    expire_in = [8,9,10].sample.hours
    token = Redis::HashKey.new(ACCESS_TOKEN_KEY, expireat: -> {Time.now + expire_in})

    if !token.all.present? || token['expired_at'].to_i < Time.now.to_i
      token['access_token'] = SecureRandom.hex(10)
      token['expired_at'] = (Time.now + expire_in).to_i
    end

    @token = token.all
  end

  def authorize_access_token
    request_token = request.headers['Access-Token']

    render json: {message: 'Invalid access token.', success: false}, status: :unauthorized and return unless token_effect?(request_token)
  end

  def api_controller?
    self.class.to_s.index('Api::') == 0
  end

  protected

  def should_skip?
    Rails.env.test?
  end

  def token_effect?(token)
    return true if should_skip?

    @current_token = current_token
    (@current_token['expired_at'].to_i > Time.now.to_i) && token == @current_token['access_token']
  end
end

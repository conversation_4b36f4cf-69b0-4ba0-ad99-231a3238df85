class ListsController < ApplicationController
  include FavoritesSharedActions
  include SorcerySharedActions

  before_action :require_login
  before_action :set_list, only: [:show, :edit, :update, :destroy, :export]
  load_and_authorize_resource

  def index
    filter_params = {term: query_params}
    filter_params = {range: {list_items_count: { from: 3, include_lower: false}}} unless params[:user_id].present?
    #@lists = List.includes(:user).where(query_params).page(params[:page]).order(order_string)

    @lists = List.search body: {
      query: {
        function_score: {
          query:{
            bool: {
              must: {match_all: {}},
              filter: [filter_params]
            }
          },
					functions: [
						{
							field_value_factor: {
								field: 'follows_count',
								modifier: 'log1p',
								factor: '0.5'
							}
						},
						{
							field_value_factor: {
								field: 'list_items_count',
								modifier: 'log1p',
								factor: '0.3'
							}
						},
						{
							gauss: {
								created_at: {
									origin: 'now',
									# 偏移范围
									scale: '3d',
									# 跌落速率
									offset: '1d',
									decay: '0.5'
								}
							},
							weight: 2
						}
					],
					score_mode: "sum"
        }
      }
    }, includes: [:user], per_page: params[:per_page] || List::default_per_page, page: params[:page]

    if params.has_key?(:user_id)
      @user = User.find(params[:user_id])

      # 如果是查看当前登录用户的目录，显示所有目录（包括私有）
      if logged_in? && current_user.id == @user.id
        # 直接查询所有目录（包括私有目录）
        @lists = List.where(user_id: current_user.id).page(params[:page]).per(params[:per_page] || List::default_per_page)
        @count = @lists.total_count
        set_seo_meta "我的目录"
      else
        # 其他用户只能看到公开目录
        @lists = List.public_lists.where(user_id: @user.id).page(params[:page]).per(params[:per_page] || List::default_per_page)
        @count = @lists.total_count
        set_seo_meta "#{@user.name}的目录"
      end
      render layout: 'panel', template: 'lists/panel' and return
    else
      #@lists = @lists.where('list_items_count > 0')
      set_seo_meta "游戏目录"
    end

    respond_to do |format|
      format.json { render json: { lists: render_to_string('lists/_array', layout: false, formats: [:html])}}
      format.html
    end
  end

  def show
    @list_items = @list.list_items.includes(subject: [:maker, :ranks, :tags]).order('list_items.created_at desc nulls last, list_items.id desc nulls last').page(1)
    @new_item = ListItem.new
    subject_ids = @list_items.collect{|item| item.subject_id}

    ranks = @list.user.ranks.where(subject_id: subject_ids)
    @ranks = ranks.inject({}) do |hash, rank|
      hash[rank.subject_id] = Rank.scores[rank.score.to_sym]
      hash
    end

    set_seo_meta @list.name
  end

  def new
    @list = List.new

    @title = t('views.new_list')

    set_seo_meta @title
  end

  def edit
    render template: 'lists/new'
  end

  def create
    @list = List.new(list_params)
    @list.user = current_user

    respond_to do |format|
      if @list.save
        format.html { redirect_to @list, notice: 'List was successfully created.' }
        format.json { render :show, status: :created, location: @list }
      else
        format.html { render :new }
        format.json { render json: @list.errors, status: :unprocessable_entity }
      end
    end
  end

  def update
    respond_to do |format|
      if @list.update(list_params)
        format.html { redirect_to @list, notice: 'List was successfully updated.' }
        format.json { render :show, status: :ok, location: @list }
      else
        flash[:error] = @list.errors.full_messages
        format.html { render :edit }
        format.json { render json: @list.errors, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @list.destroy

    render json: {message: "ok", success: true}, status: :ok
  end

  def export
    # 目录中不存在条目时，不导出
    if @list.list_items.empty?
      flash[:error] = "目录中不存在条目，无法导出"
      redirect_to @list and return
    end

    @list_items = @list.list_items.includes(:subject).order('list_items.weight desc nulls last, list_items.id desc nulls last')

    content = @list_items.map do |item|
      "#{WEB_HOST_DOMAIN}/subjects/#{item.subject_id}"
    end.join("\r\n")

    send_data content, filename: "#{@list.name}_links.txt", type: "text/plain", disposition: "attachment"
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_list
      @list = List.find(params[:id])
    end

    def query_params
      params.permit(:name, :user_id)
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def list_params
      params.require(:list).permit(:name, :description, :is_public)
    end

    def item_params
      params.require(:list_item).permit(:subject_id, :comment, :weight)
    end

    def current_ability
      @current_ability ||= Ability.new(current_user, @list)
    end
end

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function checkRunningProcesses() {
  console.log('\n🔍 正在检查可能运行的后台进程...\n');
  try {
    const output = execSync(process.platform === 'win32' ? 'tasklist' : 'ps aux').toString();
    const keywords = ['node', 'npm', 'pnpm', 'yarn', 'vite', 'webpack', 'next', 'nuxt', 'nodemon'];
    const matches = output.split('\n').filter(line =>
      keywords.some(keyword => line.toLowerCase().includes(keyword))
    );
    if (matches.length > 0) {
      console.log('⚠️ 发现可能相关的后台进程：\n');
      matches.forEach(line => console.log('   ', line.trim()));
    } else {
      console.log('✅ 没有发现相关后台进程。');
    }
  } catch (err) {
    console.error('❌ 检查进程时出错：', err.message);
  }
}

function checkPackageJson() {
  console.log('\n🔍 正在分析 package.json...\n');
  const packagePath = path.resolve('package.json');
  if (!fs.existsSync(packagePath)) {
    console.log('⚠️ 当前目录下找不到 package.json');
    return;
  }

  const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const postInstall = pkg.scripts?.postinstall;
  if (postInstall) {
    console.log('⚠️ 检测到 postinstall 脚本：', postInstall);
  } else {
    console.log('✅ 未检测到 postinstall 脚本。');
  }

  const possibleTools = ['vite', 'webpack', 'next', 'nuxt', 'nodemon'];
  const deps = { ...pkg.dependencies, ...pkg.devDependencies };
  const usedTools = possibleTools.filter(t => deps && deps[t]);

  if (usedTools.length > 0) {
    console.log('⚠️ 使用了自动构建相关工具：', usedTools.join(', '));
  } else {
    console.log('✅ 未发现可能导致自动安装的构建工具。');
  }
}

function checkGitHooks() {
  console.log('\n🔍 检查 git hooks...\n');
  const gitHooksDir = path.resolve('.git/hooks');
  const huskyDir = path.resolve('.husky');

  let hooksFound = false;

  if (fs.existsSync(gitHooksDir)) {
    const hooks = fs.readdirSync(gitHooksDir);
    if (hooks.length > 0) {
      console.log('⚠️ 检测到 .git/hooks：');
      hooks.forEach(h => console.log('   ', h));
      hooksFound = true;
    }
  }

  if (fs.existsSync(huskyDir)) {
    const hooks = fs.readdirSync(huskyDir);
    if (hooks.length > 0) {
      console.log('⚠️ 检测到 .husky 钩子：');
      hooks.forEach(h => console.log('   ', h));
      hooksFound = true;
    }
  }

  if (!hooksFound) {
    console.log('✅ 没有发现 Git 钩子。');
  }
}

function checkMonorepo() {
  console.log('\n🔍 检查是否为 Monorepo...\n');
  const rootFiles = fs.readdirSync(process.cwd());
  const indicators = ['pnpm-workspace.yaml', 'turbo.json', 'lerna.json', 'workspaces'];

  const packagePath = path.resolve('package.json');
  let isMonorepo = false;

  if (fs.existsSync(packagePath)) {
    const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    if (pkg.workspaces) isMonorepo = true;
  }

  for (const f of indicators) {
    if (rootFiles.includes(f)) {
      isMonorepo = true;
      break;
    }
  }

  console.log(isMonorepo ? '⚠️ 当前项目可能是 Monorepo。' : '✅ 当前项目不是 Monorepo。');
}

// Run all checks
checkRunningProcesses();
checkPackageJson();
checkGitHooks();
checkMonorepo();

